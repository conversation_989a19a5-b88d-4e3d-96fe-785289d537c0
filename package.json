{"name": "wp-calypso", "author": "Automattic Inc.", "version": "18.13.0", "description": "A pure REST-API and JS based version of the WordPress.com admin.", "private": true, "workspaces": {"packages": ["client", "desktop", "apps/*", "packages/*", "test/e2e"]}, "bugs": "https://github.com/Automattic/wp-calypso/issues", "homepage": "https://github.com/Automattic/wp-calypso", "license": "GPL-2.0-or-later", "repository": {"type": "git", "url": "https://github.com/Automattic/wp-calypso.git", "directory": "/"}, "browserslist": {"wpcom": ["extends @wordpress/browserslist-config"], "defaults": ["extends @wordpress/browserslist-config"], "evergreen": ["last 2 Chrome versions", "last 1 ChromeAndroid versions", "last 2 Firefox versions", "last 2 Safari versions", "last 3 iOS versions", "last 2 Edge versions", "last 2 Opera versions", "Firefox ESR", "Electron >= 26", "unreleased Chrome versions", "unreleased ChromeAndroid versions", "unreleased Firefox versions", "unreleased FirefoxAndroid versions", "unreleased Edge versions", "unreleased Opera versions"], "server": ["current node"], "test": ["current node"]}, "engines": {"node": "^v22.9.0", "yarn": "^4.0.0"}, "scripts": {"analyze-bundles": "yarn run build-client-stats && webpack-bundle-analyzer client/stats.json public/evergreen -h 127.0.0.1 -p 9898 -s gzip", "analyze-bundles:server": "yarn run build-server-stats && webpack-bundle-analyzer client/stats-server.json build -h 127.0.0.1 -p 9898 -s parsed", "analyze-icfy": "yarn run build-client-stats && node --max-old-space-size=6144 bin/icfy-analyze.js", "build": "./bin/build-packages-if-needed.sh && yarn run build-static && yarn run build-css && run-p -s 'build-devdocs:*' && run-p -s build-server build-client-if-prod", "build-client": "BROWSERSLIST_ENV=evergreen yarn webpack --config client/webpack.config.js --stats-preset errors-only", "build-client-if-prod": "node -e \"process.env.CALYPSO_ENV === 'production' && process.exit(1)\" || { yarn run build-client && yarn run build-languages-if-enabled }", "build-client-stats": "NODE_ENV=production CALYPSO_ENV=production EMIT_STATS=true yarn run build-client", "build-css": "run-p -s 'build-css:*'", "build-css:directly": "node bin/render-stylesheet.js --in client/assets/stylesheets/directly.scss --out public/directly.css && yarn run postcss public/directly.css", "build-css:reader-mobile": "node bin/render-stylesheet.js --in client/assets/stylesheets/reader-mobile.scss --out public/reader-mobile.css && yarn run postcss public/reader-mobile.css", "build-desktop": "cd desktop && yarn run build", "build-desktop:app": "cd desktop && yarn run build", "build-desktop:config": "cd desktop && yarn run build:config", "build-desktop:secrets": "cd desktop && yarn run build:secrets", "build-devdocs:search-index": "node bin/generate-devdocs-search-index.js", "build-devdocs:selectors-index": "node bin/generate-devdocs-selectors-index.js", "build-docker": "node bin/build-docker.js", "build-languages": "yarn run translate && node bin/build-languages.js", "build-languages-if-enabled": "node -e \"(process.env.BUILD_TRANSLATION_CHUNKS === 'true' || process.env.ENABLE_FEATURES === 'use-translation-chunks') && process.exit(1)\" || yarn run build-languages", "build-packages": "{ [ \"${SKIP_TSC:-}\" = \"true\" ] || tsc --build packages/tsconfig.json; } && yarn workspaces foreach --all --parallel --verbose run prepare", "build-server": "mkdir -p build && BROWSERSLIST_ENV=server webpack --config client/webpack.config.node.js --stats-preset errors-only && yarn run build-server:copy-modules", "build-server-stats": "NODE_ENV=production CALYPSO_ENV=production EMIT_STATS=true yarn run build-server", "build-server:copy-modules": "node -e \"process.env.CALYPSO_ENV === 'production' && process.exit(1)\" || node bin/copy-production-modules.js", "build-static": "npx ncp static public && npx ncp client/lib/service-worker public --filter='service-worker(/service-worker\\.js)?$'", "calypso-doctor": "./node_modules/.bin/calypso-doctor", "clean": "yarn run clean:packages && yarn run clean:build && yarn run clean:public && yarn run clean:translations", "clean:build": "rm -rf build client/server/bundler/*.json client/server/devdocs/search-index.js || true", "clean:packages": "yarn workspaces foreach --all --parallel --verbose --exclude 'wp-calypso' run clean", "clean:public": "rm -rf public", "clean:translations": "rm -rf build/strings calypso-strings.pot chunks-map.*.json || true", "components:storybook:start": "yarn workspace @automattic/components run storybook:start", "composite-checkout:storybook:start": "echo 'Deprecated, run `yarn workspace @automattic/composite-checkout run storybook:start` instead'", "distclean": "yarn run clean && rm -rf **/node_modules **/.cache .yarn/install-state.gz || true", "docker": "docker run -it --name wp-calypso --rm -p 80:3000 wp-calypso", "docker-jetpack-cloud": "docker run -it --env CALYPSO_ENV=jetpack-cloud-production --name wp-calypso --rm -p 80:3000 wp-calypso", "docker-a8c-for-agencies": "docker run -it --env CALYPSO_ENV=a8c-for-agencies-production --name wp-calypso --rm -p 80:3000 wp-calypso", "eslint-branch": "node bin/eslint-branch.js", "feature-search": "node bin/feature-search.js", "generate": "generate-calypso-package", "install-if-no-packages": "node bin/install-if-no-packages.js", "lint": "run-s -s 'lint:*'", "lint:config-defaults": "node bin/validate-config-keys.js", "lint:css": "stylelint \"**/*.{css,scss}\"", "lint:js": "eslint --ext .js,.jsx,.ts,.tsx,.mjs,.json --cache .", "lint:mixedindent": "mixedindentlint --ignore-comments \"client/**/*.scss\" \"assets/**/*.scss\" \"!build/**\" \"!node_modules/**\" \"!public/**\"", "lint:unused-state-action-types": "bin/find-unused-state-action-types.sh", "postcss": "postcss -r --config packages/calypso-build/postcss.config.js", "postinstall": "yarn run build-packages && husky install", "reformat-files": "./node_modules/.bin/prettier --ignore-path .eslint<PERSON>ore --write \"**/*.{js,jsx,json,ts,tsx}\"", "search:storybook:start": "echo 'Deprecated, run `yarn workspace @automattic/search run storybook:start` instead'", "site-admin:storybook:start": "yarn workspace @automattic/site-admin run storybook:start", "start": "npx check-node-version --package && node bin/welcome.js && yarn run build && yarn run start-build", "start:debug": "NODE_OPTIONS='--max-old-space-size=6144' SOURCEMAP='eval-source-map' yarn start", "start-mock-wordpress-com": "MOCK_WORDPRESSDOTCOM=1 yarn run start", "start-build": "BROWSERSLIST_ENV=evergreen node build/server.js | bunyan -o short", "start-jetpack-cloud": "CALYPSO_ENV=jetpack-cloud-development yarn run start", "start-jetpack-cloud-p": "PORT=3001 CALYPSO_ENV=jetpack-cloud-development yarn run build-server && PORT=3001 CALYPSO_ENV=jetpack-cloud-development yarn run start-build", "start-a8c-for-agencies": "CALYPSO_ENV=a8c-for-agencies-development yarn run start", "start-a8c-for-agencies-p": "PORT=3002 CALYPSO_ENV=a8c-for-agencies-development yarn run build-server && PORT=3002 CALYPSO_ENV=a8c-for-agencies-development yarn run start-build", "storybook:start": "storybook dev -c ./.storybook -p 6006", "storybook:build": "storybook build -c ./.storybook", "test": "run-s -s test-client test-packages test-server test-build-tools", "test-build-tools": "jest -c=test/build-tools/jest.config.js", "test-client": "TZ=UTC jest -c=test/client/jest.config.js", "test-client:watch": "yarn run test-client --watch", "test-desktop:e2e": "echo 'Deprecated, run `cd desktop && yarn run test:e2e` instead'", "test-integration": "jest -c=test/integration/jest.config.js", "test-integration:watch": "yarn run test-integration --watch", "test-apps": "jest -c=test/apps/jest.config.js", "test-apps:watch": "yarn run test-apps --watch", "test-packages": "jest -c=test/packages/jest.config.js", "test-packages:watch": "yarn run test-packages --watch", "test-server": "jest -c=test/server/jest.config.js", "test-server:coverage": "yarn run test-server --coverage", "test-server:watch": "yarn run test-server --watch", "translate": "rm -rf build/strings && mkdir -p public && yarn translate:dependencies && yarn translate:calypso", "translate:dependencies": "rm -rf build/dependency-strings && mkdir -p build/strings && wp-babel-makepot './node_modules/@wordpress/**/build-module/**/*.js' --ignore '**/*.native.js' --base './' --dir './build/dependency-strings' --output './build/strings/calypso-dependencies.pot'", "translate:calypso": "wp-babel-makepot './{client,packages,apps}/**/*.{js,jsx,ts,tsx}' --ignore '**/node_modules/**,**/test/**,**/*.d.ts' --base './' --dir './build/strings' --output './public/calypso-strings.pot'", "tsc": "NODE_OPTIONS='--max-old-space-size=4096' tsc", "typecheck": "run-p 'typecheck-*'", "typecheck-client": "yarn run tsc --project client", "typecheck-apps": "find apps -depth 2 -name tsconfig.json | xargs -n 1 -P 4 -I {} yarn tsc --noEmit --project {}", "typecheck-packages": "tsc --build packages/tsconfig.json", "vscode:link": "node bin/set-up-vs-code.js", "vscode:unlink": "node bin/set-up-vs-code.js --unlink", "whybundled": "NODE_ENV=production CALYPSO_ENV=production EMIT_STATS=withreasons CONCATENATE_MODULES=false yarn run build-client && whybundled client/stats.json", "whybundled:server": "NODE_ENV=production CALYPSO_ENV=production EMIT_STATS=withreasons CONCATENATE_MODULES=false yarn run build-server && whybundled client/stats-server.json"}, "dependencies": {"@automattic/calypso-babel-config": "workspace:^", "@automattic/calypso-build": "workspace:^", "@automattic/calypso-color-schemes": "workspace:^", "@automattic/calypso-doctor": "workspace:^", "@automattic/calypso-products": "workspace:^", "@automattic/calypso-razorpay": "workspace:^", "@automattic/calypso-router": "workspace:^", "@automattic/charts": "^0.23.1", "@automattic/color-studio": "^4.1.0", "@automattic/command-palette": "workspace:^", "@automattic/components": "workspace:^", "@automattic/data-stores": "workspace:^", "@automattic/domain-search": "workspace:^", "@automattic/help-center": "workspace:^", "@automattic/i18n-utils": "workspace:^", "@automattic/languages": "workspace:^", "@automattic/launchpad": "workspace:^", "@automattic/number-formatters": "^1.0.1", "@automattic/plans-grid-next": "workspace:^", "@automattic/privacy-toolset": "workspace:^", "@automattic/typography": "workspace:^", "@automattic/urls": "workspace:^", "@automattic/verbum-block-editor": "workspace:^", "@automattic/viewport": "workspace:^", "@automattic/wp-babel-makepot": "workspace:^", "@automattic/wpcom-template-parts": "workspace:^", "@automattic/zendesk-client": "workspace:^", "@gravatar-com/quick-editor": "^0.8.0", "@paypal/react-paypal-js": "^8.7.0", "@types/cookie": "^0.6.0", "@types/debug": "^4.1.12", "@types/fast-json-stable-stringify": "^2.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.17", "@types/node": "^22.15.3", "@types/page": "^1.11.9", "@types/qs": "^6.9.7", "@types/react": "^18.3.23", "@types/react-modal": "^3.16.3", "@types/react-transition-group": "^4.4.4", "@types/store": "^2.0.5", "@types/validator": "^13.7.1", "@types/webpack-env": "^1.18.8", "@types/wordpress__block-editor": "^11.5.16", "@wordpress/base-styles": "^5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/block-library": "^9.23.0", "@wordpress/blocks": "^14.12.0", "@wordpress/commands": "^1.23.0", "@wordpress/components": "^29.9.0", "@wordpress/data": "^10.23.0", "@wordpress/element": "^6.23.0", "@wordpress/format-library": "^5.23.0", "@wordpress/i18n": "^5.23.0", "@wordpress/icons": "^10.23.0", "@wordpress/is-shallow-equal": "^5.23.0", "@wordpress/keyboard-shortcuts": "^5.23.0", "@wordpress/keycodes": "^4.23.0", "@wordpress/primitives": "^4.23.0", "@wordpress/rich-text": "^7.23.0", "@wordpress/url": "^4.23.0", "@wordpress/viewport": "^6.23.0", "@wp-playground/client": "^1.1.0", "animate.css": "^4.1.1", "browserslist": "^4.24.5", "calypso": "workspace:^", "cmdk": "^0.2.1", "d3-time-format": "^4.1.0", "debug": "^4.4.1", "enhanced-resolve": "5.9.3", "http-proxy-middleware": "^3.0.5", "i18n-calypso": "workspace:^", "i18n-calypso-cli": "workspace:^", "jsdom": "^20.0.3", "lodash": "^4.17.21", "lottie-web": "^5.9.6", "moment": "^2.30.1", "photon": "workspace:^", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.52.2", "react-intersection-observer": "^9.4.3", "react-modal": "^3.16.3", "redux": "^5.0.1", "swiper": "^10.3.1", "wpcom": "workspace:^"}, "optionalDependencies": {"fsevents": "^2.3.3"}, "devDependencies": {"@automattic/calypso-eslint-overrides": "workspace:^", "@automattic/calypso-jest": "workspace:^", "@automattic/calypso-package-generator": "workspace:^", "@automattic/calypso-storybook": "workspace:^", "@automattic/eslint-plugin-json": "workspace:^", "@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/register": "^7.27.1", "@babel/runtime": "^7.27.1", "@pmmmwh/react-refresh-webpack-plugin": "0.5.15", "@signal-noise/stylelint-scales": "^2.0.3", "@storybook/react": "^8.6.14", "@tanstack/eslint-plugin-query": "^5.14.6", "@testing-library/jest-dom": "^6.6.3", "@types/chroma-js": "^2.4.5", "@types/eslint": "^9.6.1", "@types/ga-gtag": "^1.2.0", "@types/gtag.js": "^0.0.20", "@types/jest-when": "^3.5.5", "@types/superagent": "^4.1.24", "@types/wordpress__blocks": "^12.5.17", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@wordpress/eslint-plugin": "^22.9.0", "@wordpress/prettier-config": "^4.23.0", "@wordpress/stylelint-config": "^23.15.0", "babel-loader": "^8.2.3", "bunyan": "^1.8.15", "chalk": "^4.1.2", "check-node-version": "^4.0.2", "chroma-js": "^2.6.0", "css-loader": "^6.11.0", "doctrine": "^3.0.0", "dotenv-webpack": "^8.1.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-inclusive-language": "^2.2.1", "eslint-plugin-jest": "^27.9.0", "eslint-plugin-jsdoc": "^46.10.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-md": "^1.0.19", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-wpcalypso": "workspace:^", "eslint-plugin-you-dont-need-lodash-underscore": "^6.14.0", "gettext-parser": "^4.0.3", "glob": "^7.2.3", "globby": "^10.0.2", "husky": "^7.0.4", "jest": "^29.7.0", "jest-canvas-mock": "^2.5.2", "jest-environment-jsdom": "^29.7.0", "jest-teamcity": "^1.12.0", "loader-utils": "^1.4.2", "lunr": "^2.3.9", "mixedindentlint": "^1.2.0", "ncp": "^2.0.0", "nock": "^13.5.6", "npm-run-all": "^4.1.5", "postcss": "^8.5.3", "postcss-cli": "^9.0.1", "postcss-custom-properties": "^12.1.11", "prettier": "npm:wp-prettier@3.0.3", "react-refresh": "^0.17.0", "readline-sync": "^1.4.10", "recursive-copy": "^2.0.14", "resize-observer-polyfill": "^1.5.1", "sass": "1.54.0", "sass-loader": "^13.3.3", "source-map": "^0.7.4", "stackframe": "^1.1.1", "stacktrace-gps": "^3.0.3", "storybook": "^8.6.14", "stylelint": "^16.15.0", "stylelint-scss": "^6.4.0", "tslib": "^2.8.1", "typescript": "5.8.3", "webpack": "^5.99.8", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.10.0", "webpack-dev-middleware": "^5.3.4", "whybundled": "^2.0.0", "yargs": "^17.0.1"}, "resolutions": {"@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/wordpress__block-editor": "npm:noop-package@1.0.0", "@types/wordpress__editor": "npm:noop-package@1.0.0", "keytar@npm:7.7.0/node-addon-api": "3.1.0", "lzma-native": "^8.0.6", "node-abi": "3.35.0", "@wordpress/a11y": "4.23.0", "@wordpress/annotations": "3.23.0", "@wordpress/api-fetch": "7.23.0", "@wordpress/autop": "4.23.0", "@wordpress/babel-plugin-import-jsx-pragma": "5.23.0", "@wordpress/babel-plugin-makepot": "6.23.0", "@wordpress/babel-preset-default": "8.23.0", "@wordpress/base-styles": "^5.23.0", "@wordpress/blob": "4.23.0", "@wordpress/block-directory": "5.23.0", "@wordpress/block-editor": "^14.18.0", "@wordpress/block-library": "^9.23.0", "@wordpress/block-serialization-default-parser": "5.23.0", "@wordpress/block-serialization-spec-parser": "5.23.0", "@wordpress/blocks": "^14.12.0", "@wordpress/browserslist-config": "6.23.0", "@wordpress/components": "^30.1.0", "@wordpress/compose": "7.23.0", "@wordpress/core-data": "7.23.0", "@wordpress/create-block-tutorial-template": "4.23.0", "@wordpress/create-block": "4.66.0", "@wordpress/custom-templated-path-webpack-plugin": "2.13.0", "@wordpress/customize-widgets": "5.23.0", "@wordpress/data-controls": "4.23.0", "@wordpress/data": "^10.23.0", "@wordpress/dataviews": "^7.0.0", "@wordpress/date": "5.23.0", "@wordpress/dependency-extraction-webpack-plugin": "^6.24.0", "@wordpress/deprecated": "4.23.0", "@wordpress/docgen": "2.23.0", "@wordpress/dom-ready": "4.23.0", "@wordpress/dom": "4.23.0", "@wordpress/e2e-test-utils": "11.23.0", "@wordpress/e2e-tests": "8.23.0", "@wordpress/edit-post": "8.23.0", "@wordpress/edit-site": "6.23.0", "@wordpress/edit-widgets": "6.23.0", "@wordpress/editor": "14.23.0", "@wordpress/element": "^6.23.0", "@wordpress/escape-html": "3.23.0", "@wordpress/eslint-plugin": "^22.9.0", "@wordpress/experiments": "0.8.0", "@wordpress/format-library": "^5.23.0", "@wordpress/hooks": "4.23.0", "@wordpress/html-entities": "4.23.0", "@wordpress/i18n": "^5.23.0", "@wordpress/icons": "^10.23.0", "@wordpress/interface": "9.8.0", "@wordpress/is-shallow-equal": "^5.23.0", "@wordpress/jest-console": "8.23.0", "@wordpress/jest-preset-default": "12.23.0", "@wordpress/jest-puppeteer-axe": "7.23.0", "@wordpress/keyboard-shortcuts": "^5.23.0", "@wordpress/keycodes": "^4.23.0", "@wordpress/lazy-import": "2.23.0", "@wordpress/library-export-default-webpack-plugin": "2.14.0", "@wordpress/list-reusable-blocks": "5.23.0", "@wordpress/media-utils": "5.23.0", "@wordpress/notices": "5.23.0", "@wordpress/npm-package-json-lint-config": "5.23.0", "@wordpress/nux": "9.23.0", "@wordpress/plugins": "7.23.0", "@wordpress/postcss-plugins-preset": "5.23.0", "@wordpress/postcss-themes": "6.23.0", "@wordpress/preferences-persistence": "2.23.0", "@wordpress/preferences": "4.23.0", "@wordpress/prettier-config": "^4.23.0", "@wordpress/primitives": "^4.23.0", "@wordpress/priority-queue": "3.23.0", "@wordpress/private-apis": "^1.23.0", "@wordpress/project-management-automation": "2.23.0", "@wordpress/react-i18n": "4.23.0", "@wordpress/readable-js-assets-webpack-plugin": "^3.24.0", "@wordpress/redux-routine": "5.23.0", "@wordpress/reusable-blocks": "5.23.0", "@wordpress/rich-text": "^7.23.0", "@wordpress/server-side-render": "5.23.0", "@wordpress/shortcode": "4.23.0", "@wordpress/style-engine": "2.23.0", "@wordpress/stylelint-config": "^23.15.0", "@wordpress/token-list": "3.23.0", "@wordpress/url": "^4.23.0", "@wordpress/viewport": "^6.23.0", "@wordpress/warning": "3.23.0", "@wordpress/widgets": "4.23.0", "@wordpress/wordcount": "4.23.0"}, "packageManager": "yarn@4.0.2", "dependenciesMeta": {"@react-spring/core@9.3.0": {"built": false}}}