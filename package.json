{"name": "woocommerce-services", "version": "3.1.0", "scripts": {"start": "cross-env NODE_ENV=development CALYPSO_CLIENT=true webpack-dev-server --hot --inline --watch --content-base dist --port 8085 --host 0.0.0.0", "watch": "cross-env NODE_ENV=development CALYPSO_CLIENT=true webpack --watch", "prepare": "cd wp-calypso && npm ci && cd .. && husky install", "dist": "cross-env NODE_ENV=production DISABLE_FEATURES=wpcom-user-bootstrap CALYPSO_CLIENT=true webpack", "analyze": "cross-env ANALYZE=true NODE_ENV=production DISABLE_FEATURES=wpcom-user-bootstrap CALYPSO_CLIENT=true webpack", "eslint": "eslint tasks/ client/", "eslint:fix": "eslint --fix tasks/ client/", "test": "npm run test-client", "test-client": "TZ='UTC' jest -c=tests/client/jest.config.js", "test-client:watch": "npm run -s test-client -- --watch", "prerelease": "npm run i18n && node tasks/i18n-download && npm run i18njson", "release": "node tasks/release.js", "i18n": "cross-env NODE_ENV=i18n DISABLE_FEATURES=wpcom-user-bootstrap CALYPSO_CLIENT=true webpack && node tasks/i18n", "i18njson": "node tasks/i18njson", "update-deps": "rm -f npm-shrinkwrap.json && rm -rf node_modules && npm install && npm shrinkwrap", "build": "echo yes | npm run release && cp release/woocommerce-services.zip ./", "up": "docker-compose up --build --force-recreate -d && ./bin/docker-setup.sh", "down": "docker-compose down", "test:e2e-docker-up": "env WP_VERSION=6.7.2 TRAVIS_MARIADB_VERSION=10.8.3 npx wc-e2e docker:up", "test:e2e-docker-down": "npx wc-e2e docker:down", "test:e2e-docker-ping": "bash ./tests/bin/wait-for-e2e-build.sh", "test:e2e": "npx wc-e2e test:e2e", "test:e2e:record": "WC_E2E_SCREENSHOTS=1 npx wc-e2e test:e2e", "test:e2e-dev": "npx wc-e2e test:e2e-dev", "tube:setup": "./bin/jurassic-tube-setup.sh", "tube:start": "./docker/bin/jt/tunnel.sh", "tube:stop": "./docker/bin/jt/tunnel.sh break"}, "repository": {"type": "git", "url": "git://github.com/Automattic/woocommerce-services"}, "license": "GPL-2.0", "description": "Connect allthethings", "devDependencies": {"@babel/preset-typescript": "^7.27.1", "@slack/web-api": "^5.15.0", "@woocommerce/woocommerce-rest-api": "^1.0.1", "@wordpress/base-styles": "^3.3.0", "@wordpress/dependency-extraction-webpack-plugin": "^2.9.0", "@wordpress/e2e-test-utils": "^5.4.1", "archiver": "^1.3.0", "babel-jest": "24.7.1", "babel-plugin-dynamic-import-node": "2.2.0", "chai": "4.2.0", "chai-enzyme": "1.0.0-beta.1", "chalk": "^3.0.0", "commander": "^4.1.1", "confirm-simple": "^1.0.3", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "1.12.1", "enzyme-to-json": "3.3.5", "file-loader": "^4.3.0", "glob": "^7.1.6", "globby": "^6.1.0", "husky": "^7.0.0", "i18n-calypso-cli": "^1.0.0", "jest": "^24.9.0", "jest-puppeteer": "^4.4.0", "lint-staged": "^10.5.4", "moment": "^2.29.1", "moment-timezone-data-webpack-plugin": "^1.4.0", "nock": "10.0.6", "optimize-css-assets-webpack-plugin": "^5.0.4", "po2json": "^0.4.5", "postcss-safe-parser": "^4.0.2", "puppeteer": "^13.7.0", "redux-mock-store": "^1.5.4", "request": "^2.88.2", "shelljs": "^0.7.6", "sinon": "7.3.1", "sinon-chai": "3.3.0", "webpack-bundle-analyzer": "^3.9.0", "xgettext-js": "^1.1.0", "yarnhook": "^0.5.1"}, "dependencies": {"@automattic/calypso-color-schemes": "^1.0.0-alpha.1", "@automattic/color-studio": "^2.4.0", "@automattic/format-currency": "^1.0.1", "@babel/cli": "7.1.2", "@babel/core": "^7.4.0", "@babel/plugin-proposal-class-properties": "7.1.0", "@babel/plugin-proposal-export-default-from": "7.0.0", "@babel/plugin-proposal-export-namespace-from": "7.0.0", "@babel/plugin-syntax-dynamic-import": "7.0.0", "@babel/plugin-syntax-jsx": "7.0.0", "@babel/plugin-transform-runtime": "7.1.0", "@babel/polyfill": "7.0.0", "@babel/preset-env": "7.1.0", "@babel/preset-react": "7.0.0", "@babel/runtime": "7.1.2", "@hot-loader/react-dom": "^16.14.0", "@woocommerce/e2e-environment": "^0.2.2", "@woocommerce/woocommerce-rest-api": "^1.0.1", "@wordpress/babel-preset-default": "^5.0.1", "@wordpress/base-styles": "^3.3.0", "@wordpress/components": "^11.1.5", "@wordpress/element": "^2.19.1", "autoprefixer": "^6.7.4", "babel-eslint": "10.0.1", "babel-loader": "8.0.4", "babel-plugin-add-module-exports": "1.0.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-export-extensions": "6.22.0", "classnames": "^2.2.5", "cross-env": "^3.1.4", "css-loader": "^3.5.1", "d3-array": "2.0.3", "d3-axis": "1.0.12", "d3-scale": "3.0.0", "d3-selection": "1.4.0", "d3-shape": "1.3.5", "debug": "^2.6.1", "deep-freeze": "0.0.1", "dompurify": "^0.8.5", "email-validator": "2.0.4", "eslint": "5.16.0", "eslint-config-prettier": "3.1.0", "eslint-config-wpcalypso": "4.0.1", "eslint-loader": "2.1.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-jest": "22.4.1", "eslint-plugin-jsx-a11y": "6.2.1", "eslint-plugin-react": "7.12.4", "eslint-plugin-wpcalypso": "4.0.2", "exports-loader": "0.7.0", "gridicons": "^2.1.2", "html-loader": "0.5.5", "i18n-calypso": "^1.8.4", "imports-loader": "0.8.0", "is-my-json-valid": "^2.20.5", "loader-utils": "1.1.0", "lodash": "^4.17.21", "mini-css-extract-plugin": "0.4.4", "node-sass": "^4.14.1", "object-path-immutable": "^0.5.1", "objectpath": "^1.2.1", "postcss-custom-properties": "^8.0.9", "postcss-loader": "3.0.0", "postcss-url": "^8.0.0", "prop-types": "15.5.10", "qs": "6.5.1", "react": "^16.14.0", "react-dom": "^16.14.0", "react-hot-loader": "^4.13.0", "react-modal": "^3.16.3", "react-redux": "^5.1.2", "redux": "^3.7.2", "redux-thunk": "^2.3.0", "reselect": "^2.5.4", "sass-loader": "6.0.7", "striptags": "^3.2.0", "style-loader": "^1.3.0", "terser-webpack-plugin": "1.1.0", "thread-loader": "1.2.0", "webpack": "^4.46.0", "webpack-cli": "^3.3.11", "webpack-dev-server": "^3.11.2", "whatwg-fetch": "^2.0.2", "wrap-loader": "^0.2.0"}, "config": {"wp_org_slug": "woocommerce-services", "use_gh_release_notes": true}}