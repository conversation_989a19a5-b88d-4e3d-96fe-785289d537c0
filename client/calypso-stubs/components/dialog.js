/**
 * Stub for components/dialog module
 * This provides a simple dialog component
 */

import React from 'react';

// Simple Dialog component
export default function Dialog( { isVisible, onClose, children, className = '', ...props } ) {
	if ( ! isVisible ) {
		return null;
	}

	return (
		<div className={ `dialog-overlay ${ className }` } { ...props }>
			<div className="dialog-content">
				<button className="dialog-close" onClick={ onClose }>
					×
				</button>
				{ children }
			</div>
		</div>
	);
}

// Additional dialog-related exports that might be needed
export const DialogContent = ( { children, className = '' } ) => (
	<div className={ `dialog-body ${ className }` }>{ children }</div>
);

export const DialogFooter = ( { children, className = '' } ) => (
	<div className={ `dialog-footer ${ className }` }>{ children }</div>
);

export const DialogHeader = ( { children, className = '' } ) => (
	<div className={ `dialog-header ${ className }` }>{ children }</div>
);
