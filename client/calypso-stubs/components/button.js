/**
 * Stub for components/button module
 * This provides a simple button component
 */

import React from 'react';

// Simple Button component
export default function Button( { 
	children, 
	onClick, 
	disabled = false, 
	primary = false,
	scary = false,
	compact = false,
	busy = false,
	className = '',
	type = 'button',
	...props 
} ) {
	const buttonClass = [
		'button',
		primary && 'is-primary',
		scary && 'is-scary',
		compact && 'is-compact',
		busy && 'is-busy',
		className
	].filter(Boolean).join(' ');

	return (
		<button 
			type={ type }
			className={ buttonClass }
			onClick={ onClick }
			disabled={ disabled || busy }
			{ ...props }
		>
			{ busy ? 'Loading...' : children }
		</button>
	);
}
