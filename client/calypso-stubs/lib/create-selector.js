/**
 * Stub for lib/create-selector module
 * This provides a simple selector creation utility
 */

// Simple createSelector implementation
export default function createSelector( dependencies, resultFunc ) {
	// Simple memoization based on the first dependency
	let lastArgs = null;
	let lastResult = null;

	return function( state, ...args ) {
		const currentArgs = dependencies.map( dep => 
			typeof dep === 'function' ? dep( state, ...args ) : dep 
		);

		// Simple equality check
		if ( lastArgs && JSON.stringify( currentArgs ) === JSON.stringify( lastArgs ) ) {
			return lastResult;
		}

		lastArgs = currentArgs;
		lastResult = resultFunc( ...currentArgs );
		return lastResult;
	};
}
