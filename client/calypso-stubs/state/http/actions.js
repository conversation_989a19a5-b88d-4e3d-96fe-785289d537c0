/**
 * Stub for state/http/actions module
 * This provides HTTP action creators for the data layer
 */

// Simple HTTP action creators
export const httpRequest = ( options ) => {
	return {
		type: 'HTTP_REQUEST',
		...options
	};
};

export const httpSuccess = ( options ) => {
	return {
		type: 'HTTP_SUCCESS',
		...options
	};
};

export const httpError = ( options ) => {
	return {
		type: 'HTTP_ERROR',
		...options
	};
};
