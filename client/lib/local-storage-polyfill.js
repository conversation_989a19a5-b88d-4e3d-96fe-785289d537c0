/**
 * Local Storage Polyfill
 * Provides localStorage functionality for environments that don't have it
 */

export default function localStoragePolyfill() {
	// Check if localStorage is already available
	if ( typeof window !== 'undefined' && window.localStorage ) {
		return;
	}

	// Simple in-memory storage fallback
	const storage = {};

	if ( typeof window !== 'undefined' ) {
		window.localStorage = {
			getItem: function( key ) {
				return storage[ key ] || null;
			},
			setItem: function( key, value ) {
				storage[ key ] = String( value );
			},
			removeItem: function( key ) {
				delete storage[ key ];
			},
			clear: function() {
				for ( const key in storage ) {
					delete storage[ key ];
				}
			},
			get length() {
				return Object.keys( storage ).length;
			},
			key: function( index ) {
				const keys = Object.keys( storage );
				return keys[ index ] || null;
			}
		};
	}
}
